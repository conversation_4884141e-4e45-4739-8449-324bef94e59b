:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --text-color: #fff;

    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;

    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);

    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    --gradient-title: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-card: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
    --gradient-button: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));

    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05);
    --shadow-hero: 0 20px 60px rgba(0, 0, 0, 0.4);

    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;

    /* Artist Specific Colors */
    --artist-card-bg: rgba(20, 22, 30, 0.95);
    --artist-card-border: rgba(255, 255, 255, 0.1);
    --artist-glass-bg: rgba(255, 255, 255, 0.05);
    --artist-hero-bg: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%);
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background: var(--background-primary);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Navbar Styles */
header {
    background: linear-gradient(
        90deg,
        rgba(19, 21, 26, 0.95) 0%,
        rgba(26, 29, 36, 0.92) 60%,
        rgba(27, 27, 27, 0.1) 100%
    );
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.menu a {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative;
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after,
.menu a:not([aria-current="page"]):hover::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--button-gradient);
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px;
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    box-shadow: 0 0 12px rgba(0, 224, 255, 0.3), 0 0 24px rgba(0, 224, 255, 0.2), inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--gradient-header);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary);
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative;
    overflow: hidden;
    z-index: 0;
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1;
    border-radius: inherit;
}

.dropdown a:hover {
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1;
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: var(--error-color);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}


/* Modern Container Styles */
.container {
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: 120px 20px 30px;
    min-height: calc(100vh - 80px);
    box-sizing: border-box;
}

.section {
    margin: 2.5rem 0;
    text-align: center;
    background: var(--artist-card-bg);
    border: 1px solid var(--artist-card-border);
    border-radius: 20px;
    box-shadow: var(--shadow-glass);
    padding: 2rem 1.5rem;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    transition: all var(--transition-normal) ease;
}

.section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card), var(--shadow-glass);
    border-color: rgba(255, 255, 255, 0.15);
}

.section-divider {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 1.2rem;
}

.divider-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0.2rem 0 0.2rem 0;
    font-size: 2.2rem;
    /* Remove background, border, box-shadow */
    background: none;
    border: none;
    box-shadow: none;
    /* Use gradient for icon color */
    color: transparent;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
}

.divider-line {
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 2px;
    margin: 0.1rem 0;
}

.section h2 {
    font-size: 2.2rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    margin-top: 0.5rem;
    position: relative;
    display: block;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    letter-spacing: 1.5px;
    /* Gradient text style */
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    text-shadow: none;
    padding-bottom: 0.3em;
}

.section h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    border-radius: 3px;
}

.artist-header {
    position: relative;
    padding: 3rem 2rem;
    border-radius: 24px;
    overflow: hidden;
    margin-bottom: 2.5rem;
    background: var(--artist-card-bg);
    border: 1px solid var(--artist-card-border);
    box-shadow: var(--shadow-glass);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    transition: all var(--transition-normal) ease;
}

.artist-header:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hero), var(--shadow-glass);
    border-color: rgba(255, 255, 255, 0.15);
}

.artist-header-backdrop {
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, rgba(var(--cosmic-pink-rgb), 0.15), rgba(var(--neon-blue-rgb), 0.15));
    z-index: 0;
    opacity: 0.8;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

.artist-photo-container {
    position: relative;
    z-index: 1;
    width: 220px;
    height: 220px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
    margin-right: 32px;
    flex-shrink: 0;
    border: 4px solid rgba(var(--neon-blue-rgb), 0.3);
}

.artist-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    display: block;
}

.artist-photo-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(120deg, rgba(var(--neon-blue-rgb), 0.15), rgba(var(--cosmic-pink-rgb), 0.15));
    border-radius: 50%;
    pointer-events: none;
}

.artist-info {
    z-index: 1;
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.artist-name h1 {
    font-size: 2.5rem;
    font-weight: 800;
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 0.25em;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.verified-badge {
    color: var(--neon-blue);
    font-size: 1.2em;
    vertical-align: middle;
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-bottom: 0.5em;
}

.artist-bio-container {
    margin-bottom: 1em;
    transition: max-height 0.5s cubic-bezier(0.4,0,0.2,1), padding 0.5s cubic-bezier(0.4,0,0.2,1);
    overflow: hidden;
}

.artist-bio {
    color: var(--text-secondary);
    font-size: 1.05rem;
    line-height: 1.6;
}

.artist-stats {
    display: flex;
    gap: 32px;
    margin-bottom: 1em;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-icon {
    color: var(--neon-blue);
    font-size: 1.3em;
}

.stat-number {
    font-size: 1.1em;
    font-weight: 700;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.95em;
}

.artist-actions {
    display: flex;
    gap: 12px;
    margin-top: 15px;
}

.action-btn {
    padding: 12px 24px;
    border-radius: 25px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all var(--transition-speed);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 15px;
    background: var(--button-gradient);
    color: white;
    box-shadow: var(--button-shadow);
}

.action-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--button-hover-shadow);
}

.play-btn:hover {
    box-shadow: 0 8px 25px rgba(var(--neon-blue-rgb), 0.4), 0 8px 25px rgba(var(--cosmic-pink-rgb), 0.4);
}

.follow-btn {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.follow-btn:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.share-btn {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.share-btn:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Share Modal Styles */
.share-modal {
    position: fixed;
    inset: 0;
    background: rgba(0,0,0,0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.share-modal[hidden] {
    display: none !important;
}

.share-modal-content {
    background: #181a20;
    border-radius: 18px;
    padding: 2rem 2.5rem;
    box-shadow: 0 8px 32px rgba(0,0,0,0.35);
    min-width: 320px;
    max-width: 90vw;
    color: var(--text-primary);
    text-align: center;
}

.share-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.share-option {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.7rem 1.2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.7rem;
    transition: background 0.2s, transform 0.2s;
}

.share-option:hover {
    background: var(--cosmic-pink);
    transform: translateY(-2px) scale(1.03);
}

@keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    100% { background-position: 200% 50%; }
}

/* Track List */
.track-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--artist-card-bg);
    border: 1px solid var(--artist-card-border);
    border-radius: 16px;
    transition: transform var(--transition-normal) ease, box-shadow var(--transition-normal) ease;
    margin: 0 12px; /* Add horizontal space between track cards */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.track-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2), 
                0 0 0 2px rgba(var(--neon-blue-rgb), 0.3);
}

.track-item[aria-current="true"] {
    outline: 2px solid var(--neon-blue);
    box-shadow: 0 0 0 4px rgba(0,224,255,0.15), 0 8px 32px var(--neon-blue), 0 8px 32px var(--cosmic-pink);
    z-index: 2;
    background: linear-gradient(90deg, rgba(0,224,255,0.08), rgba(255,0,110,0.08));
}

.track-item:focus {
    outline: 2px solid var(--cosmic-pink);
    z-index: 2;
}

/* Removed .track-list, .track-number, .track-title, .track-meta, .track-duration for code cleanliness. */

/* Albums Section */
.albums-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: var(--card-spacing);
    margin: var(--section-spacing) 0;
}

.album-card {
    background: var(--artist-card-bg);
    border: 1px solid var(--artist-card-border);
    border-radius: 16px;
    box-shadow: var(--shadow-glass);
    min-width: 220px;
    max-width: 260px;
    flex: 0 0 auto;
    overflow: hidden;
    transition: box-shadow var(--transition-normal), transform var(--transition-normal);
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 12px; /* Add horizontal space between album cards */
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.album-card:hover {
    box-shadow: 0 8px 32px var(--neon-blue), 0 8px 32px var(--cosmic-pink);
    transform: translateY(-4px) scale(1.03);
    border-color: var(--neon-blue);
}

.album-artwork {
    width: 100%;
    aspect-ratio: 1/1;
    object-fit: cover;
    border-radius: var(--card-radius) var(--card-radius) 0 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
}

.album-info {
    padding: 18px 14px 14px 14px;
    width: 100%;
    text-align: center;
}

.album-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.album-year {
    font-size: 0.95rem;
    color: var(--text-muted);
    margin-bottom: 0;
}

@media (max-width: 600px) {
    .album-card {
        min-width: 60vw;
        max-width: 80vw;
    }
    .album-info {
        padding: 10px 4px 8px 4px;
    }
}

/* Similar Artists Section */
.similar-artists {
    margin: 4rem 0;
    padding: 2rem 0;
}

.similar-artists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.similar-artist-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: slideUp 0.5s ease forwards;
    opacity: 0;
    animation-delay: calc(var(--item-index, 0) * 0.1s);
}

.similar-artist-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--button-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
    opacity: 0.7;
}

.similar-artist-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2), 0 0 20px rgba(var(--neon-blue-rgb), 0.15);
}

.similar-artist-card:hover::before {
    transform: scaleX(1);
}

.similar-artist-photo {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.similar-artist-card:hover .similar-artist-photo {
    border-color: rgba(var(--neon-blue-rgb), 0.3);
    box-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.2);
}

.similar-artist-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.similar-artist-card:hover .similar-artist-photo img {
    transform: scale(1.1);
}

.similar-artist-card h3 {
    font-size: 1.2rem;
    margin: 0.5rem 0;
    font-weight: 600;
    transition: color 0.3s ease;
}

.similar-artist-card:hover h3 {
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.similar-artist-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* Removed unused .track-list, .track-number, .similar-artists, .similar-artists-grid, .similar-artist-card, .similar-artist-photo, .view-artist-btn, and related keyframes for code cleanliness. */

/* Demo Tracks Section */
.track-carousel {
    padding-bottom: 1rem;
}

.track-item {
    background: var(--card-bg);
    border: var(--card-border);
    border-radius: var(--card-radius);
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
    display: flex;
    min-width: 320px;
    max-width: 350px;
    align-items: center;
    gap: 1rem;
    padding: 1.2rem 1rem;
    position: relative;
    transition: box-shadow 0.2s, transform 0.2s;
    margin: 0 12px; /* Add horizontal space between track cards */
}

.track-item.playing, .track-item:hover {
    box-shadow: 0 8px 32px var(--neon-blue), 0 8px 32px var(--cosmic-pink);
    transform: translateY(-4px) scale(1.03);
    border-color: var(--neon-blue);
}

.track-cover {
    width: 64px;
    height: 64px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    flex-shrink: 0;
    position: relative;
}

.track-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.track-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.track-details h3 {
    font-size: 1.1rem;
    font-weight: 700;
    margin: 0 0 2px 0;
    color: var(--text-primary);
}

.track-info {
    font-size: 0.95rem;
    color: var(--text-muted);
    margin-bottom: 0.2rem;
}

.track-progress {
    width: 100%;
    height: 5px;
    background: var(--track-progress-bg);
    border-radius: 3px;
    margin: 6px 0 0 0;
    overflow: hidden;
    position: relative;
}

.track-progress-bar {
    height: 100%;
    background: var(--track-progress-active);
    width: 0%;
    border-radius: 3px;
    transition: width 0.2s;
}

.track-controls {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.3rem;
    align-items: center;
}

.track-play-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    transition: background 0.2s, transform 0.2s;
}

.track-play-btn:active {
    transform: scale(0.95);
}

.track-item.playing .track-play-btn {
    background: var(--cosmic-pink);
}

.track-times {
    display: flex;
    gap: 0.5em;
    font-size: 0.95em;
    color: var(--text-muted);
    margin: 2px 0 0 0;
    align-items: center;
}
.track-mute-btn {
    background: rgba(255,255,255,0.08);
    color: var(--text-primary);
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1em;
    cursor: pointer;
    transition: background 0.2s, transform 0.2s;
}
.track-mute-btn:focus, .track-play-btn:focus {
    outline: 2px solid var(--neon-blue);
    z-index: 2;
}
.track-mute-btn:hover {
    background: var(--cosmic-pink);
    color: #fff;
}
.track-loading {
    margin-left: 0.5em;
    color: var(--neon-blue);
    font-size: 1.1em;
    display: inline-flex;
    align-items: center;
}
.track-loading[hidden] {
    display: none !important;
}
.track-play-btn .fa-pause {
    display: none;
}
.track-item.playing .track-play-btn .fa-play {
    display: none;
}
.track-item.playing .track-play-btn .fa-pause {
    display: inline;
}
@media (hover: none) and (pointer: coarse) {
    .track-controls {
        gap: 0.8rem;
    }
    .track-mute-btn, .track-play-btn {
        width: 40px;
        height: 40px;
        font-size: 1.2em;
    }
}

/* Gallery Section */
.artist-gallery {
    margin: 3rem 0;
    text-align: center; /* Center section content */
}
.gallery-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center; /* Center grid items horizontally */
    gap: 1.5rem;
}
.gallery-item {
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}
.gallery-item::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 50%);
    opacity: 0;
    transition: opacity 0.3s ease;
}
.gallery-item:hover::after {
    opacity: 1;
}
.gallery-img, .gallery-video {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 12px;
    transition: transform 0.5s ease;
}
.gallery-item:hover .gallery-img,
.gallery-item:hover .gallery-video {
    transform: scale(1.1);
}
.gallery-video {
    background: #000;
}
.gallery-modal {
    position: fixed;
    inset: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 3000;
}
.gallery-modal[hidden] {
    display: none !important;
}
.gallery-modal-content {
    background: #23243a;
    border-radius: 16px;
    padding: 24px 18px;
    min-width: 280px;
    max-width: 90vw;
    max-height: 90vh;
    color: #fff;
    position: relative;
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
}
.gallery-modal-content img, .gallery-modal-content video {
    max-width: 80vw;
    max-height: 70vh;
    border-radius: 12px;
}
.close-gallery-modal {
    position: absolute;
    top: 12px;
    right: 18px;
    font-size: 2em;
    color: #fff;
    cursor: pointer;
    z-index: 10;
}
.close-gallery-modal:focus {
    outline: 2px solid var(--neon-blue);
}
@media (max-width: 600px) {
    .gallery-grid {
        grid-template-columns: 1fr 1fr;
        gap: 0.7rem;
    }
    .gallery-img, .gallery-video {
        height: 100px;
    }
    .gallery-modal-content img, .gallery-modal-content video {
        max-width: 98vw;
        max-height: 60vh;
    }
}

/* Concerts & Special Events Section */
.artist-events {
    margin: 3rem 0;
}
.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}
.event-card {
    background: var(--card-bg);
    border: var(--card-border);
    border-radius: var(--card-radius);
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    display: flex;
    align-items: flex-start;
    padding: 1.2rem 1rem;
    gap: 1.2rem;
    position: relative;
    transition: box-shadow 0.2s, transform 0.2s;
}
.event-card.upcoming:hover {
    box-shadow: 0 8px 32px var(--neon-blue), 0 8px 32px var(--cosmic-pink);
    transform: translateY(-4px) scale(1.03);
    border-color: var(--neon-blue);
}
.event-date {
    background: var(--gradient-primary);
    color: #fff;
    border-radius: 10px;
    min-width: 54px;
    min-height: 54px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.1em;
    box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    margin-right: 0.7rem;
    padding: 6px 0;
}
.event-day {
    font-size: 1.5em;
    line-height: 1;
}
.event-month {
    font-size: 1em;
    text-transform: uppercase;
    letter-spacing: 1px;
}
.event-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}
.event-title {
    font-size: 1.15rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2px;
}
.event-location {
    font-size: 0.98rem;
    color: var(--text-secondary);
    margin-bottom: 2px;
    display: flex;
    align-items: center;
    gap: 0.4em;
}
.event-description {
    font-size: 0.97rem;
    color: var(--text-muted);
    margin-bottom: 4px;
}
.event-ticket {
    display: inline-block;
    background: var(--gradient-primary);
    color: #fff;
    border-radius: 6px;
    padding: 7px 18px;
    font-weight: 600;
    text-decoration: none;
    margin-top: 6px;
    transition: background 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
}
.event-ticket:hover {
    background: var(--cosmic-pink);
    color: #fff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}
.past-label {
    background: #444;
    color: #fff;
    border-radius: 6px;
    padding: 6px 14px;
    font-size: 0.95em;
    font-weight: 600;
    margin-top: 8px;
    display: inline-block;
    opacity: 0.7;
}
.event-card.past {
    opacity: 0.7;
    filter: grayscale(0.3);
}
@media (max-width: 600px) {
    .events-grid {
        grid-template-columns: 1fr;
        gap: 0.7rem;
    }
    .event-card {
        flex-direction: column;
        align-items: stretch;
        padding: 0.8rem 0.5rem;
    }
    .event-date {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
}

.gallery-item.hovered, .gallery-item:focus {
    outline: 2px solid var(--neon-blue);
    box-shadow: 0 0 0 4px rgba(0,224,255,0.15), 0 8px 32px var(--neon-blue), 0 8px 32px var(--cosmic-pink);
    z-index: 2;
}
.gallery-caption {
    margin-top: 1rem;
    color: var(--neon-blue);
    font-size: 1.1em;
    text-align: center;
    font-weight: 600;
}

.gallery-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--cosmic-pink);
    color: #fff;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    font-size: 2em;
    cursor: pointer;
    z-index: 20;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
}
.gallery-close-btn:hover, .gallery-close-btn:focus {
    background: var(--neon-blue);
    outline: 2px solid #fff;
}
.gallery-download-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 0.5em 1.2em;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    margin-top: 1em;
    text-decoration: none;
    display: inline-block;
    transition: background 0.2s;
}
.gallery-download-btn:hover, .gallery-download-btn:focus {
    background: var(--cosmic-pink);
    color: #fff;
    outline: 2px solid var(--neon-blue);
}
@media (hover: none) and (pointer: coarse) {
    .gallery-close-btn, .gallery-download-btn {
        font-size: 1.5em;
        padding: 0.7em 1.5em;
    }
}

.skip-link {
    position: absolute;
    left: -999px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
    background: var(--neon-blue);
    color: #fff;
    z-index: 3000;
    padding: 0.5em 1em;
    border-radius: 8px;
    font-weight: 700;
    transition: left 0.2s;
}
.skip-link:focus {
    left: 10px;
    top: 10px;
    width: auto;
    height: auto;
    outline: 2px solid var(--cosmic-pink);
}
#aria-live-region {
    position: absolute;
    left: -9999px;
    top: auto;
    width: 1px;
    height: 1px;
    overflow: hidden;
}

@media (max-width: 768px) {
    .artist-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        padding: 1.5rem 1rem;
    }
    
    .artist-photo-container {
        margin-right: 0;
        margin-bottom: 1.5rem;
    }
    
    .artist-stats {
        justify-content: center;
    }
    
    .artist-actions {
        justify-content: center;
    }
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(var(--neon-blue-rgb), 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(var(--neon-blue-rgb), 0); }
    100% { box-shadow: 0 0 0 0 rgba(var(--neon-blue-rgb), 0); }
}

.track-item.playing {
    border-color: var(--neon-blue);
    animation: pulse 2s infinite;
}

.track-item:focus-within {
    outline: 2px solid var(--neon-blue);
    box-shadow: 0 0 0 4px rgba(0,224,255,0.15);
    transform: translateY(-4px);
}

.track-item .track-play-btn:focus, 
.track-item .track-mute-btn:focus {
    outline: 2px solid #fff;
    box-shadow: 0 0 0 4px rgba(255,255,255,0.2);
}

.floating-play-btn {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    cursor: pointer;
    z-index: 100;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.floating-play-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(var(--neon-blue-rgb), 0.5);
}
.floating-play-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(var(--neon-blue-rgb), 0.5);
}

/* Mini Player Styles */
.mini-player {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3001;
    background: var(--gradient-header);
    color: #fff;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.7rem 1.5rem;
    box-shadow: 0 -2px 16px rgba(0,0,0,0.25);
    border-top: 1px solid rgba(255,255,255,0.08);
    transition: transform 0.3s;
}

.mini-player[hidden] {
    display: none !important;
}

.mini-player-cover img {
    width: 48px;
    height: 48px;
    border-radius: 8px;
    object-fit: cover;
    background: #222;
}

.mini-player-info {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.mini-player-title {
    font-weight: 700;
    font-size: 1.05rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mini-player-meta {
    font-size: 0.95rem;
    color: var(--text-muted);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mini-player-controls {
    display: flex;
    gap: 0.5rem;
}

.mini-player-controls button {
    background: none;
    border: none;
    color: #fff;
    font-size: 1.2em;
    cursor: pointer;
    padding: 0.3em 0.6em;
    border-radius: 50%;
    transition: background 0.2s;
}
.mini-player-controls button:hover, .mini-player-controls button:focus {
    background: var(--cosmic-pink);
    outline: none;
}

.mini-player-progress {
    display: flex;
    align-items: center;
    gap: 0.5em;
    min-width: 120px;
    flex: 1 1 180px;
}

#mini-player-slider {
    width: 80px;
    accent-color: var(--neon-blue);
    background: transparent;
}

.mini-player-close {
    position: absolute;
    top: 8px;
    right: 16px;
    background: none;
    border: none;
    color: #fff;
    font-size: 2rem;
    cursor: pointer;
    z-index: 10;
    opacity: 0.7;
    transition: opacity 0.2s, color 0.2s;
}
.mini-player-close:hover, .mini-player-close:focus {
    color: var(--cosmic-pink);
    opacity: 1;
    outline: none;
}