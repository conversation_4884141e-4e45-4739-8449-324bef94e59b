:root {
    /* Theme Colors */
    --background-primary: #0D1117;
    --background-secondary: #121212;
    --header-gradient-start: #13151a;
    --header-gradient-end: #1a1d24;
    
    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    
    /* Brand Colors */
    --electric-violet: #6F00FF;
    --electric-violet-rgb: 111, 0, 255;
    --neon-blue: #00E0FF;
    --neon-blue-rgb: 0, 224, 255;
    --cosmic-pink: #FF006E;
    --cosmic-pink-rgb: 255, 0, 110;
    --cyber-lime: #A7FF4A;
    
    /* Functional Colors */
    --accent-color: var(--cosmic-pink);
    --error-color: #ff4646;
    --hover-color: rgba(255, 255, 255, 0.1);
    
    /* Gradients */
    --gradient-primary: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-header: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
    --gradient-shine: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    
    /* Shadows */
    --shadow-button: 0 5px 15px rgba(0, 0, 0, 0.2);
    --shadow-button-hover: 0 8px 25px rgba(0, 224, 255, 0.3), 0 8px 25px rgba(255, 0, 110, 0.3);
    --shadow-card: 0 8px 32px rgba(56, 12, 97, 0.15);
    
    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;

    /* Profile Specific Colors */
    --profile-card-bg: rgba(20, 22, 30, 0.95);
    --profile-card-border: rgba(255, 255, 255, 0.1);
    --profile-glass-bg: rgba(255, 255, 255, 0.05);
    --profile-hero-bg: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%);

    /* Enhanced Gradients */
    --gradient-title: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    --gradient-card: linear-gradient(135deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.02) 100%);
    --gradient-button: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));

    /* Enhanced Shadows */
    --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05);
    --shadow-hero: 0 20px 60px rgba(0, 0, 0, 0.4);

    --player-bg: linear-gradient(135deg, #1a1f25 0%, #2c3e50 100%);
    --player-card: rgba(255,255,255,0.04);
    --player-border: 1.5px solid rgba(255,255,255,0.12);
    --player-radius: 22px;
    --player-shadow: 0 8px 32px rgba(56, 12, 97, 0.18);
    --player-glow: 0 0 24px 6px var(--neon-blue), 0 0 48px 12px var(--cosmic-pink);
}



/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background: var(--background-primary);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Modern Container Layout */
.container {
    flex: 1;
    max-width: 1600px;
    margin: 0 auto;
    padding: 120px 40px 30px;
    min-height: calc(100vh - 80px);
    box-sizing: border-box;
}

/* Profile Page Layout */
.profile-container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto 40px auto;
    padding: 2rem 1rem;
    background: var(--profile-card-bg);
    border: 1px solid var(--profile-card-border);
    border-radius: 20px;
    box-shadow: var(--shadow-glass);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
}

.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.profile-section h1 {
    font-size: 2.2rem;
    font-weight: 800;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 0 20px rgba(var(--neon-blue-rgb), 0.3);
    margin-bottom: 1.5rem;
}

.profile-info {
    display: flex;
    align-items: center;
    gap: 2rem;
    background: rgba(255,255,255,0.04);
    border-radius: 14px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.profile-avatar {
    width: 110px;
    height: 110px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 2px 8px rgba(0,0,0,0.18);
    border: 3px solid var(--neon-blue);
}

.profile-details h2 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5em;
    color: var(--text-primary);
}

.profile-details p {
    font-size: 1rem;
    color: var(--text-secondary);
    margin: 0.2em 0;
}

/* Navbar Styles */
header {
    background: rgba(19, 21, 26, 0.95); /* Remove gradient, use solid/semi-transparent dark */
    padding: 12px 20px;
    color: #fff;
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    /* width: 100%; */ /* Redundant */
    z-index: 1000;
    box-sizing: border-box;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}



.logo img {
    width: 80px;
    height: 80px;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.logo img:hover {
    transform: scale(1.05);
}

/* Menu Styles */

.menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.menu a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: 500;    
    padding: 0.5rem 1rem;
    border-radius: 20px;
    position: relative; /* For ::after positioning */
}

.menu a:hover {
    /* color: var(--accent-color); Removed to implement border effect */
}

.menu a[aria-current="page"] {
    color: var(--neon-blue);
    position: relative;
    text-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

.menu a[aria-current="page"]::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue));
    background-size: 200% 100%;
    border-radius: 2px;
    animation: navShimmer 3s linear infinite;
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(0, 224, 255, 0.3);
}

/* New hover effect for non-current menu items */
.menu a:not([aria-current="page"])::after {
    content: '';
    position: absolute;
    bottom: -5px; /* Matches current page indicator's position */
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink), var(--neon-blue)); /* Same gradient as current page */
    background-size: 200% 100%; /* Required for the shimmer effect */
    border-radius: 2px;
    animation: navShimmer 3s linear infinite; /* Apply the shimmer animation */
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px rgba(var(--neon-blue-rgb), 0.3); /* Apply similar shadow */
    transform: scaleX(0); /* Initially hidden by scaling width to 0 */
    transform-origin: left; /* Animation expands from the left */
    transition: transform 0.3s ease-out; /* Smooth transition for scaling */
}

.menu a:not([aria-current="page"]):hover::after {
    transform: scaleX(1); /* Expand to full width on hover */
}

@keyframes navShimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.user-profile {
    position: relative;
    margin-left: 15px; /* Reduced from 20px */
    cursor: pointer;
}

.profile-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.profile-button:hover {
    transform: scale(1.05);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
        0 0 12px rgba(0, 224, 255, 0.3),
        0 0 24px rgba(0, 224, 255, 0.2),
        inset 0 0 8px rgba(255, 255, 255, 0.1);
    filter: brightness(1.1);
}

.profile-button:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.profile-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    transition: box-shadow 0.3s, filter 0.3s;
}

/* Navbar profile icon animation for active page */
.user-profile .profile-icon[aria-current="page"],
.user-profile .profile-icon.active-profile {
    box-shadow: 0 0 0 0 var(--neon-blue), 0 0 12px 4px var(--cosmic-pink), 0 0 24px 8px var(--neon-blue);
    animation: profileGlow 2.2s infinite alternate;
    filter: brightness(1.1) saturate(1.2);
}

@keyframes profileGlow {
    0% {
        box-shadow: 0 0 0 0 var(--neon-blue), 0 0 8px 2px var(--cosmic-pink), 0 0 16px 4px var(--neon-blue);
        filter: brightness(1.08) saturate(1.1);
    }
    100% {
        box-shadow: 0 0 0 0 var(--cosmic-pink), 0 0 16px 6px var(--neon-blue), 0 0 32px 12px var(--cosmic-pink);
        filter: brightness(1.18) saturate(1.25);
    }
}

.dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: linear-gradient(315deg, var(--header-gradient-start) 0%, var(--header-gradient-end) 100%);
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    min-width: 180px;
    z-index: 1000;
    margin-top: 10px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: none;
}

.dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 20px;
    width: 12px;
    height: 12px; /* Consider matching dropdown bg more closely */
    background: var(--header-gradient-start); /* Example: using a variable for consistency */
    transform: rotate(45deg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    z-index: -1;
}

/* Show dropdown on hover */
.user-profile:hover .dropdown {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto; /* Allow clicks when visible */
    transition: transform 0.2s ease, opacity 0.2s ease, visibility 0s;
}

/* Create a hover area to prevent dropdown from closing too quickly */
.user-profile::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 20px; /* Invisible area to maintain hover */
    background: transparent;
}

/* Keep dropdown visible when hovering over it */
.dropdown:hover {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

/* For accessibility - keep the old class for keyboard users */
.dropdown.show {
    visibility: visible;
    transform: translateY(0);
    opacity: 1;
    pointer-events: auto;
}

.dropdown ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.dropdown li {
    margin: 0.25rem 0;
}

.dropdown a {
    color: var(--text-primary); /* Changed from --text-color */
    text-decoration: none;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border-radius: 8px;
    font-weight: 500;
    position: relative; /* For pseudo-element positioning */
    overflow: hidden;   /* To clip the pseudo-element with border-radius */
    z-index: 0;         /* Establish stacking context for ::before z-index */
}

.dropdown a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(var(--neon-blue-rgb), 0.2), rgba(var(--cosmic-pink-rgb), 0.2), rgba(var(--neon-blue-rgb), 0.2));
    opacity: 0;
    transition: opacity 0.2s ease;
    z-index: -1; /* Place background behind the text */
    border-radius: inherit; /* Inherit parent's border-radius */
}

.dropdown a:hover {
    /* Background is now handled by ::before pseudo-element */
    transform: translateX(3px);
}

.dropdown a:hover::before {
    opacity: 1; /* Fade in the background */
}

.dropdown a:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.dropdown .logout-button {
    color: #ff5a5a; /* Or use your --error-color variable: var(--error-color); */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 0.5rem;
    padding-top: 0.75rem;
}



/* ===== ENHANCED PROFILE HERO SECTION ===== */
.profile-hero {
    position: relative;
    max-width: 1200px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    overflow: hidden;
    padding: 4rem 2rem 3rem 2rem;
    margin-bottom: 2.5rem;
    margin-top: 110px;
    border-radius: 24px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 0 40px rgba(var(--neon-blue-rgb), 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    min-height: 320px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
}

/* Enhanced animated background for hero */
.profile-hero-bg-animated {
    position: absolute;
    inset: 0;
    z-index: 0;
    pointer-events: none;
    border-radius: inherit;
    background:
        radial-gradient(circle at 30% 20%, rgba(111,0,255,0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0,224,255,0.12) 0%, transparent 50%),
        radial-gradient(circle at 40% 90%, rgba(255,0,110,0.10) 0%, transparent 50%),
        linear-gradient(135deg, rgba(0,224,255,0.05) 0%, rgba(255,0,110,0.08) 50%, rgba(111,0,255,0.05) 100%);
    animation: profileHeroBgMove 25s ease-in-out infinite alternate;
    opacity: 0.9;
}

/* Add floating particles effect */
.profile-hero-bg-animated::before {
    content: '';
    position: absolute;
    inset: 0;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(0,224,255,0.4), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255,0,110,0.3), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.2), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(111,0,255,0.3), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particleFloat 30s linear infinite;
    opacity: 0.6;
}

@keyframes profileHeroBgMove {
    0% {
        transform: translateX(0) translateY(0) scale(1);
        filter: hue-rotate(0deg);
    }
    25% {
        transform: translateX(-10px) translateY(-5px) scale(1.02);
        filter: hue-rotate(5deg);
    }
    50% {
        transform: translateX(5px) translateY(10px) scale(0.98);
        filter: hue-rotate(10deg);
    }
    75% {
        transform: translateX(8px) translateY(-8px) scale(1.01);
        filter: hue-rotate(5deg);
    }
    100% {
        transform: translateX(0) translateY(0) scale(1);
        filter: hue-rotate(0deg);
    }
}

@keyframes particleFloat {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

.profile-hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 700px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
}

.profile-image-form {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0.5em;
}

.profile-hero-avatar-label {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.profile-hero-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow:
        0 8px 32px rgba(0,0,0,0.3),
        0 0 20px rgba(var(--neon-blue-rgb), 0.4),
        inset 0 0 0 3px rgba(255,255,255,0.1);
    border: 4px solid transparent;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink)) padding-box,
                linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink)) border-box;
    margin-bottom: 0.2em;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
}

.profile-hero-avatar::before {
    content: '';
    position: absolute;
    inset: -8px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    filter: blur(15px);
}

.profile-hero-avatar-label:hover .profile-hero-avatar,
.profile-hero-avatar-label:focus .profile-hero-avatar {
    transform: scale(1.05);
    box-shadow:
        0 12px 40px rgba(0,0,0,0.4),
        0 0 30px rgba(var(--neon-blue-rgb), 0.6),
        0 0 60px rgba(var(--cosmic-pink-rgb), 0.3);
}

.profile-hero-avatar-label:hover .profile-hero-avatar::before,
.profile-hero-avatar-label:focus .profile-hero-avatar::before {
    opacity: 0.8;
}

.profile-avatar-edit {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: var(--gradient-primary);
    color: #fff;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow:
        0 4px 12px rgba(0,0,0,0.3),
        0 0 15px rgba(var(--neon-blue-rgb), 0.4);
    border: 3px solid rgba(255,255,255,0.9);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    pointer-events: none;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.profile-avatar-edit::before {
    content: '';
    position: absolute;
    inset: -3px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    filter: blur(8px);
}

.profile-hero-avatar-label:hover .profile-avatar-edit,
.profile-hero-avatar-label:focus .profile-avatar-edit {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1.1) rotate(15deg);
    box-shadow:
        0 6px 20px rgba(0,0,0,0.4),
        0 0 25px rgba(var(--cosmic-pink-rgb), 0.6);
}

.profile-hero-avatar-label:hover .profile-avatar-edit::before,
.profile-hero-avatar-label:focus .profile-avatar-edit::before {
    opacity: 0.8;
}

.profile-settings-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255,255,255,0.08);
    border: none;
    color: var(--neon-blue);
    border-radius: 50%;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    z-index: 3;
}
.profile-settings-btn:hover,
.profile-settings-btn:focus {
    background: var(--gradient-primary);
    color: #fff;
    outline: none;
}

.profile-hero-bio {
    margin: 0.7em auto 0.2em auto;
    font-size: 1.1rem;
    color: var(--text-secondary);
    background: rgba(255,255,255,0.06);
    border-radius: 10px;
    padding: 0.7em 1.2em;
    min-height: 2.2em;
    max-width: 420px;
    outline: none;
    border: 1.5px solid transparent;
    transition: border 0.2s, background 0.2s;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    text-align: center;
    cursor: text;
}
.profile-hero-bio:focus {
    border: 1.5px solid var(--cosmic-pink);
    background: rgba(255,255,255,0.13);
    color: #fff;
}

.profile-followed-by {
    margin: 0.5em auto 0.2em auto;
    color: var(--text-secondary);
    font-size: 1rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5em;
}
.profile-followed-by i {
    color: var(--neon-blue);
    margin-right: 0.2em;
}

/* Enhanced Profile Stats Grid */
.profile-stats {
    display: flex;
    gap: 2rem;
    justify-content: center;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    z-index: 2;
}

.profile-stat-item {
    background: rgba(255,255,255,0.06);
    border-radius: 16px;
    padding: 1.5rem 2rem;
    text-align: center;
    box-shadow:
        0 4px 20px rgba(0,0,0,0.15),
        inset 0 1px 0 rgba(255,255,255,0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 120px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255,255,255,0.1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.profile-stat-item::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg,
        rgba(var(--neon-blue-rgb), 0.1) 0%,
        rgba(var(--cosmic-pink-rgb), 0.08) 50%,
        rgba(var(--electric-violet-rgb), 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.profile-stat-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 12px 40px rgba(0,0,0,0.25),
        0 0 30px rgba(var(--neon-blue-rgb), 0.3),
        0 0 60px rgba(var(--cosmic-pink-rgb), 0.2);
    border-color: rgba(var(--neon-blue-rgb), 0.3);
}

.profile-stat-item:hover::before {
    opacity: 1;
}

.profile-stat-icon {
    font-size: 2.2rem;
    margin-bottom: 0.6rem;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    display: inline-block;
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 8px rgba(var(--neon-blue-rgb), 0.4));
}

.profile-stat-item:hover .profile-stat-icon {
    transform: scale(1.2) rotate(5deg);
    filter: drop-shadow(0 0 15px rgba(var(--neon-blue-rgb), 0.8));
}

.profile-stat-number {
    font-size: 2.2rem;
    font-weight: 800;
    color: #fff;
    display: block;
    margin-bottom: 0.3rem;
    text-shadow:
        0 0 15px rgba(255,255,255,0.8),
        0 0 30px rgba(var(--neon-blue-rgb), 0.4),
        0 0 45px rgba(var(--cosmic-pink-rgb), 0.2);
    transition: all 0.3s ease;
    position: relative;
}

.profile-stat-item:hover .profile-stat-number {
    transform: scale(1.1);
    text-shadow:
        0 0 20px rgba(255,255,255,1),
        0 0 40px rgba(var(--neon-blue-rgb), 0.6),
        0 0 60px rgba(var(--cosmic-pink-rgb), 0.4);
}

.profile-stat-label {
    font-size: 0.95rem;
    color: var(--text-secondary);
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: color 0.3s ease;
}

.profile-stat-item:hover .profile-stat-label {
    color: var(--text-primary);
}

/* Focus states for accessibility */
.profile-stat-item:focus {
    outline: 2px solid var(--neon-blue);
    outline-offset: 3px;
    transform: translateY(-8px) scale(1.05);
    box-shadow:
        0 12px 40px rgba(0,0,0,0.25),
        0 0 30px rgba(var(--neon-blue-rgb), 0.5),
        0 0 60px rgba(var(--cosmic-pink-rgb), 0.3);
}

.profile-stat-item:focus::before {
    opacity: 1;
}

.profile-stat-item:focus .profile-stat-icon {
    transform: scale(1.2) rotate(5deg);
    filter: drop-shadow(0 0 15px rgba(var(--neon-blue-rgb), 0.8));
}

.profile-stat-item:focus .profile-stat-number {
    transform: scale(1.1);
    text-shadow:
        0 0 20px rgba(255,255,255,1),
        0 0 40px rgba(var(--neon-blue-rgb), 0.6),
        0 0 60px rgba(var(--cosmic-pink-rgb), 0.4);
}

.profile-stat-item:focus .profile-stat-label {
    color: var(--text-primary);
}

/* Responsive */
@media (max-width: 700px) {
    .profile-hero-content {
        gap: 0.7rem;
    }
    .profile-hero-title {
        font-size: 1.3rem;
    }
    .profile-hero-avatar {
        width: 60px;
        height: 60px;
    }
    .profile-stats {
        gap: 1rem;
    }
    .profile-stat-item {
        padding: 0.7rem 1rem;
        min-width: 70px;
    }
    .profile-stat-icon {
        font-size: 1.3rem;
    }
    .profile-stat-number {
        font-size: 1.1rem;
    }
    .profile-stat-label {
        font-size: 0.9rem;
    }
}

@media (max-width: 900px) {
    .profile-hero-header {
        flex-direction: column;
        gap: 1.2rem;
        align-items: center;
    }
    .profile-hero-userinfo {
        align-items: center;
    }
    .profile-hero-title-row {
        flex-direction: column;
        gap: 0.3em;
    }
}
@media (max-width: 600px) {
    .profile-hero {
        padding: 2rem 0.5rem 1.2rem 0.5rem;
        min-height: 120px;
    }
    .profile-hero-header {
        gap: 0.7rem;
    }
    .profile-hero-avatar {
        width: 60px;
        height: 60px;
    }
    .profile-hero-title {
        font-size: 1.3rem;
    }
}

/* Profile Header */
.profile-header {
    display: flex;
    gap: 2.5rem;
    align-items: flex-start;
    padding: 2.5rem;
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    width: 100%;
    max-width: 800px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.profile-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

/* Add backdrop gradient */
.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: -1;
    opacity: 0.5;
}

/* Profile Image Section */
.profile-image-container {
    position: relative;
    width: 140px;
    height: 140px;
    border-radius: 50%;
}

.profile-image-container img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.profile-image-container:hover img {
    transform: scale(1.02);
}

/* Edit Image Button */
.edit-image-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--button-gradient);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--button-shadow);
    transition: all 0.3s ease;
    z-index: 10;
}

.edit-image-btn i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 1.2rem;
    line-height: 1;
}

.edit-image-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--button-hover-shadow);
}

/* Profile Info */
.profile-info {
    flex: 1;
    padding-top: 0.5rem;
}

.profile-name {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    background: linear-gradient(
        45deg,
        var(--neon-blue),
        var(--cosmic-pink) 50%,
        var(--electric-violet)
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    letter-spacing: -0.5px;
}

.profile-bio {
    font-size: 1.05rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.75rem;
    line-height: 1.8;
    max-width: 90%;
    position: relative;
    overflow-y: auto;
    max-height: 120px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for the profile bio */
.profile-bio::-webkit-scrollbar {
    width: 6px;
}

.profile-bio::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb {
    background: var(--neon-blue);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(var(--neon-blue), var(--cosmic-pink));
}

/* Profile Stats */
.profile-stats {
    display: flex;
    gap: 2.5rem;
    margin-top: 1rem;
    position: relative;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    min-width: 110px;
}

.stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
}

.stat::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
}

.stat:hover::before {
    transform: scaleX(1);
}

.stat-value {
    font-size: 1.6rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.stat-icon {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 0.5rem;
    transition: transform 0.3s ease;
}

.stat:hover .stat-icon {
    transform: scale(1.2);
    color: white;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Edit Profile Button */
.edit-profile-btn {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: var(--button-gradient);
    border: none;
    color: white;
    padding: 0.4rem 0.9rem;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.edit-profile-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--button-hover-shadow);
}

/* ===== PROFILE CONTENT SECTION ===== */
.profile-content {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.content-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.content-section:hover {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

/* Profile Favorite Section */
.profile-favorite-section {
    margin: 2.5rem auto 1.5rem auto;
    max-width: 1000px;
    background: var(--profile-card-bg);
    border: 1px solid var(--profile-card-border);
    border-radius: 20px;
    box-shadow: var(--shadow-glass);
    padding: 2rem 2.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    transition: all var(--transition-normal) ease;
}

.profile-favorite-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card), var(--shadow-glass);
    border-color: rgba(255, 255, 255, 0.15);
}

.favorite-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: linear-gradient(120deg, rgba(0,224,255,0.07) 0%, rgba(255,0,110,0.07) 100%);
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.10);
    padding: 1.2rem 1.5rem;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    position: relative;
    transition: box-shadow 0.25s, transform 0.25s, background 0.25s;
    cursor: pointer;
    outline: none;
}
.favorite-card:focus,
.favorite-card:hover {
    box-shadow: 0 8px 24px rgba(0,224,255,0.13), 0 2px 10px rgba(255,0,110,0.10);
    background: linear-gradient(120deg, rgba(0,224,255,0.13) 0%, rgba(255,0,110,0.13) 100%);
    transform: translateY(-4px) scale(1.02);
    z-index: 2;
}

.favorite-img-container {
    position: relative;
    width: 90px;
    height: 90px;
    border-radius: 14px;
    overflow: hidden;
    flex-shrink: 0;
    background: rgba(0,0,0,0.13);
    display: flex;
    align-items: center;
    justify-content: center;
}
.favorite-cover-img,
.favorite-artist-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.13);
    background: #181c24;
    transition: filter 0.2s;
}
.artist-avatar-container {
    border-radius: 50%;
    width: 90px;
    height: 90px;
}
.favorite-artist-avatar {
    border-radius: 50%;
    background: #181c24;
}

.favorite-play-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0,0,0,0.38);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.25s;
    z-index: 2;
    border-radius: inherit;
}
.favorite-card:hover .favorite-play-overlay,
.favorite-card:focus .favorite-play-overlay {
    opacity: 1;
    pointer-events: auto;
}
.favorite-play-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,224,255,0.18);
    cursor: pointer;
    transition: background 0.2s, transform 0.2s;
}
.favorite-play-btn:hover,
.favorite-play-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1.1);
    outline: none;
}

.favorite-card-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3em;
    flex: 1;
}

.favorite-card-title {
    font-size: 1.15rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 0.2em;
    letter-spacing: 0.01em;
}

.favorite-card-meta {
    display: flex;
    gap: 1em;
    align-items: center;
    font-size: 0.98rem;
    color: var(--text-secondary);
    margin-bottom: 0.5em;
    flex-wrap: wrap;
}
.favorite-card-meta i {
    color: var(--neon-blue);
    margin-right: 0.3em;
}
.favorite-card-tag {
    background: rgba(0,224,255,0.09);
    color: var(--neon-blue);
    border-radius: 12px;
    padding: 0.15em 0.7em;
    font-size: 0.93em;
    font-weight: 600;
    letter-spacing: 0.01em;
}

.favorite-action-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 22px;
    padding: 0.5em 1.2em;
    font-size: 1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5em;
    cursor: pointer;
    box-shadow: var(--shadow-button);
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
    text-decoration: none;
    margin-top: 0.5em;
}
.favorite-action-btn:hover,
.favorite-action-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.05);
}

/* Section title refinement */
.profile-section-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Widen main containers and sections */
.profile-container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto 40px auto;
    padding: 2rem 1rem;
    background: linear-gradient(135deg, #181c24 0%, #23243a 60%, #181c24 100%);
    border-radius: 18px;
    box-shadow: 0 8px 32px rgba(56, 12, 97, 0.18);
}

.profile-hero {
    max-width: 1200px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    overflow: visible;
    padding: 3.5rem 2rem 2.5rem 2rem;
    margin-bottom: 2.5rem;
    margin-top: 110px;
    border-radius: 18px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    min-height: 260px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Profile Favorite Section */
.profile-favorite-section {
    margin: 2.5rem auto 1.5rem auto;
    max-width: 700px;
    background: rgba(255,255,255,0.04);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
}

.profile-favorite-section,
.profile-follow-section,
.profile-section {
    max-width: 1300px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 2rem;
    padding-right: 2rem;
}

/* Remove/reduce excessive padding if present */
.profile-favorite-section,
.profile-follow-section,
.profile-section {
    padding-left: 2rem;
    padding-right: 2rem;
}

/* Responsive: allow full width on smaller screens */
@media (max-width: 1300px) {
    .profile-container,
    .profile-hero,
    .profile-favorite-section,
    .profile-follow-section,
    .profile-section {
        max-width: 98vw;
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Responsive adjustments */
@media (max-width: 700px) {
    .profile-hero-content {
        gap: 0.7rem;
    }
    .profile-hero-title {
        font-size: 1.3rem;
    }
    .profile-hero-avatar {
        width: 60px;
        height: 60px;
    }
    .profile-stats {
        gap: 1rem;
    }
    .profile-stat-item {
        padding: 0.7rem 1rem;
        min-width: 70px;
    }
    .profile-stat-icon {
        font-size: 1.3rem;
    }
    .profile-stat-number {
        font-size: 1.1rem;
    }
    .profile-stat-label {
        font-size: 0.9rem;
    }
}

@media (max-width: 900px) {
    .profile-hero-header {
        flex-direction: column;
        gap: 1.2rem;
        align-items: center;
    }
    .profile-hero-userinfo {
        align-items: center;
    }
    .profile-hero-title-row {
        flex-direction: column;
        gap: 0.3em;
    }
}
@media (max-width: 600px) {
    .profile-hero {
        padding: 2rem 0.5rem 1.2rem 0.5rem;
        min-height: 120px;
    }
    .profile-hero-header {
        gap: 0.7rem;
    }
    .profile-hero-avatar {
        width: 60px;
        height: 60px;
    }
    .profile-hero-title {
        font-size: 1.3rem;
    }
}

/* Profile Header */
.profile-header {
    display: flex;
    gap: 2.5rem;
    align-items: flex-start;
    padding: 2.5rem;
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    width: 100%;
    max-width: 800px;
    box-shadow: 0 10px 35px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.profile-header:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

/* Add backdrop gradient */
.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(var(--neon-blue-rgb), 0.05) 0%,
        rgba(var(--cosmic-pink-rgb), 0.05) 50%,
        rgba(var(--electric-violet-rgb), 0.05) 100%
    );
    z-index: -1;
    opacity: 0.5;
}

/* Profile Image Section */
.profile-image-container {
    position: relative;
    width: 140px;
    height: 140px;
    border-radius: 50%;
}

.profile-image-container img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.profile-image-container:hover img {
    transform: scale(1.02);
}

/* Edit Image Button */
.edit-image-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--button-gradient);
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--button-shadow);
    transition: all 0.3s ease;
    z-index: 10;
}

.edit-image-btn i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    font-size: 1.2rem;
    line-height: 1;
}

.edit-image-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--button-hover-shadow);
}

/* Profile Info */
.profile-info {
    flex: 1;
    padding-top: 0.5rem;
}

.profile-name {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    background: linear-gradient(
        45deg,
        var(--neon-blue),
        var(--cosmic-pink) 50%,
        var(--electric-violet)
    );
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    letter-spacing: -0.5px;
}

.profile-bio {
    font-size: 1.05rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.75rem;
    line-height: 1.8;
    max-width: 90%;
    position: relative;
    overflow-y: auto;
    max-height: 120px;
    padding: 12px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Custom scrollbar for the profile bio */
.profile-bio::-webkit-scrollbar {
    width: 6px;
}

.profile-bio::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb {
    background: var(--neon-blue);
    border-radius: 3px;
}

.profile-bio::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(var(--neon-blue), var(--cosmic-pink));
}

/* Profile Stats */
.profile-stats {
    display: flex;
    gap: 2.5rem;
    margin-top: 1rem;
    position: relative;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    min-width: 110px;
}

.stat:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 0 15px rgba(var(--neon-blue-rgb), 0.3);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
}

.stat::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, var(--neon-blue), var(--cosmic-pink));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
    opacity: 0.7;
}

.stat:hover::before {
    transform: scaleX(1);
}

.stat-value {
    font-size: 1.6rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.stat-icon {
    font-size: 1.5rem;
    color: white;
    margin-bottom: 0.5rem;
    transition: transform 0.3s ease;
}

.stat:hover .stat-icon {
    transform: scale(1.2);
    color: white;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Edit Profile Button */
.edit-profile-btn {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    background: var(--button-gradient);
    border: none;
    color: white;
    padding: 0.4rem 0.9rem;
    border-radius: 50px;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: var(--button-shadow);
}

.edit-profile-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--button-hover-shadow);
}

/* ===== PROFILE CONTENT SECTION ===== */
.profile-content {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.content-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.content-section:hover {
    background: rgba(255, 255, 255, 0.04);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    margin: 0;
}

/* Profile Favorite Section */
.profile-favorite-section {
    margin: 2.5rem auto 1.5rem auto;
    max-width: 700px;
    background: rgba(255,255,255,0.04);
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    padding: 2rem 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.2rem;
}

.favorite-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: linear-gradient(120deg, rgba(0,224,255,0.07) 0%, rgba(255,0,110,0.07) 100%);
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.10);
    padding: 1.2rem 1.5rem;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    position: relative;
    transition: box-shadow 0.25s, transform 0.25s, background 0.25s;
    cursor: pointer;
    outline: none;
}
.favorite-card:focus,
.favorite-card:hover {
    box-shadow: 0 8px 24px rgba(0,224,255,0.13), 0 2px 10px rgba(255,0,110,0.10);
    background: linear-gradient(120deg, rgba(0,224,255,0.13) 0%, rgba(255,0,110,0.13) 100%);
    transform: translateY(-4px) scale(1.02);
    z-index: 2;
}

.favorite-img-container {
    position: relative;
    width: 90px;
    height: 90px;
    border-radius: 14px;
    overflow: hidden;
    flex-shrink: 0;
    background: rgba(0,0,0,0.13);
    display: flex;
    align-items: center;
    justify-content: center;
}
.favorite-cover-img,
.favorite-artist-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.13);
    background: #181c24;
    transition: filter 0.2s;
}
.artist-avatar-container {
    border-radius: 50%;
    width: 90px;
    height: 90px;
}
.favorite-artist-avatar {
    border-radius: 50%;
    background: #181c24;
}

.favorite-play-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0,0,0,0.38);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.25s;
    z-index: 2;
    border-radius: inherit;
}
.favorite-card:hover .favorite-play-overlay,
.favorite-card:focus .favorite-play-overlay {
    opacity: 1;
    pointer-events: auto;
}
.favorite-play-btn {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: var(--gradient-primary);
    border: none;
    color: #fff;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,224,255,0.18);
    cursor: pointer;
    transition: background 0.2s, transform 0.2s;
}
.favorite-play-btn:hover,
.favorite-play-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    transform: scale(1.1);
    outline: none;
}

.favorite-card-content {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3em;
    flex: 1;
}

.favorite-card-title {
    font-size: 1.15rem;
    font-weight: 700;
    color: #fff;
    margin-bottom: 0.2em;
    letter-spacing: 0.01em;
}

.favorite-card-meta {
    display: flex;
    gap: 1em;
    align-items: center;
    font-size: 0.98rem;
    color: var(--text-secondary);
    margin-bottom: 0.5em;
    flex-wrap: wrap;
}
.favorite-card-meta i {
    color: var(--neon-blue);
    margin-right: 0.3em;
}
.favorite-card-tag {
    background: rgba(0,224,255,0.09);
    color: var(--neon-blue);
    border-radius: 12px;
    padding: 0.15em 0.7em;
    font-size: 0.93em;
    font-weight: 600;
    letter-spacing: 0.01em;
}

.favorite-action-btn {
    background: var(--gradient-primary);
    color: #fff;
    border: none;
    border-radius: 22px;
    padding: 0.5em 1.2em;
    font-size: 1em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5em;
    cursor: pointer;
    box-shadow: var(--shadow-button);
    transition: background 0.2s, box-shadow 0.2s, transform 0.2s;
    text-decoration: none;
    margin-top: 0.5em;
}
.favorite-action-btn:hover,
.favorite-action-btn:focus {
    background: linear-gradient(45deg, var(--cosmic-pink), var(--neon-blue));
    box-shadow: var(--shadow-button-hover);
    outline: none;
    transform: scale(1.05);
}

/* Section title refinement */
.profile-section-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5em;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

/* Responsive adjustments */
@media (max-width: 700px) {
    .favorite-card {
        flex-direction: column;
        align-items: center;
        padding: 1rem 0.5rem;
        gap: 0.7rem;
        max-width: 98vw;
    }
    .favorite-img-container,
    .artist-avatar-container {
        width: 60px;
        height: 60px;
    }
    .favorite-card-title {
        font-size: 1rem;
    }
    .favorite-card-meta {
        font-size: 0.93rem;
        gap: 0.5em;
    }
    .favorite-card-tag {
        font-size: 0.9em;
    }
    .favorite-action-btn {
        font-size: 0.95em;
        padding: 0.4em 1em;
    }
}

/* Recent Activity Section */
.profile-activity-section {
    margin: 2.5rem auto 1.5rem auto;
    max-width: 1000px;
    background: var(--profile-card-bg);
    border: 1px solid var(--profile-card-border);
    border-radius: 20px;
    box-shadow: var(--shadow-glass);
    padding: 2rem 2.5rem;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    transition: all var(--transition-normal) ease;
}

.profile-activity-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card), var(--shadow-glass);
    border-color: rgba(255, 255, 255, 0.15);
}

.activity-feed {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255,255,255,0.03);
    border-radius: 12px;
    border: 1px solid rgba(255,255,255,0.05);
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: rgba(255,255,255,0.06);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
    transform: translateX(5px);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--neon-blue), var(--cosmic-pink));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin: 0 0 0.25rem 0;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* Top Genres Section */
.profile-genres-section {
    margin: 2.5rem auto 1.5rem auto;
    max-width: 1000px;
    background: var(--profile-card-bg);
    border: 1px solid var(--profile-card-border);
    border-radius: 20px;
    box-shadow: var(--shadow-glass);
    padding: 2rem 2.5rem;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    transition: all var(--transition-normal) ease;
}

.profile-genres-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card), var(--shadow-glass);
    border-color: rgba(255, 255, 255, 0.15);
}

.genres-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.genre-card {
    background: rgba(255,255,255,0.05);
    border-radius: 12px;
    padding: 1.5rem 1rem;
    text-align: center;
    border: 1px solid rgba(255,255,255,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.genre-card:hover {
    background: rgba(255,255,255,0.08);
    border-color: rgba(var(--neon-blue-rgb), 0.3);
    transform: translateY(-3px);
}

.genre-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.genre-card h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0.5rem 0 0.25rem 0;
}

.genre-card p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0 0 1rem 0;
}

.genre-progress {
    width: 100%;
    height: 4px;
    background: rgba(255,255,255,0.1);
    border-radius: 2px;
    overflow: hidden;
}

.genre-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--neon-blue), var(--cosmic-pink));
    border-radius: 2px;
    transition: width 0.5s ease;
}

/* Set specific widths for genre progress bars */
.genre-card[data-genre="lo-fi"] .genre-progress-fill { width: 45%; }
.genre-card[data-genre="electronic"] .genre-progress-fill { width: 28%; }
.genre-card[data-genre="indie"] .genre-progress-fill { width: 18%; }
.genre-card[data-genre="ambient"] .genre-progress-fill { width: 9%; }

/* Recently Played Section */
.profile-recent-section {
    margin: 2.5rem auto 1.5rem auto;
    max-width: 1000px;
    background: var(--profile-card-bg);
    border: 1px solid var(--profile-card-border);
    border-radius: 20px;
    box-shadow: var(--shadow-glass);
    padding: 2rem 2.5rem;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    transition: all var(--transition-normal) ease;
}

.profile-recent-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card), var(--shadow-glass);
    border-color: rgba(255, 255, 255, 0.15);
}

.recent-tracks {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.recent-track-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: rgba(255,255,255,0.03);
    border-radius: 10px;
    border: 1px solid rgba(255,255,255,0.05);
    transition: all 0.3s ease;
}

.recent-track-item:hover {
    background: rgba(255,255,255,0.06);
    border-color: rgba(var(--neon-blue-rgb), 0.2);
    transform: translateX(3px);
}

.recent-track-cover {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
    flex-shrink: 0;
}

.recent-track-info {
    flex: 1;
}

.recent-track-info h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.95rem;
    font-weight: 600;
    color: var(--text-primary);
}

.recent-track-info p {
    margin: 0;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.recent-track-time {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-shrink: 0;
}

.recent-track-time span {
    font-size: 0.85rem;
    color: var(--text-secondary);
    min-width: 35px;
}

.recent-track-play {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: transparent;
    border: 1px solid rgba(var(--neon-blue-rgb), 0.3);
    color: var(--neon-blue);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.recent-track-play:hover {
    background: var(--gradient-primary);
    color: white;
    border-color: transparent;
    transform: scale(1.1);
}

/* Enhanced Profile Header Actions */
.profile-hero-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 1rem;
}

.profile-hero-userinfo {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.profile-hero-title-row {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.profile-hero-title {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--neon-blue), var(--cosmic-pink));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    margin: 0;
}

.profile-status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.profile-status-badge.premium {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
}

.profile-hero-actions {
    display: flex;
    gap: 0.5rem;
}

.profile-share-btn {
    background: rgba(255,255,255,0.08);
    border: none;
    color: var(--cosmic-pink);
    border-radius: 50%;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.profile-share-btn:hover {
    background: var(--gradient-primary);
    color: white;
    transform: scale(1.05);
}

/* Follow Section Enhancement */
.profile-follow-section {
    margin: 2.5rem auto 1.5rem auto;
    max-width: 1000px;
    background: var(--profile-card-bg);
    border: 1px solid var(--profile-card-border);
    border-radius: 20px;
    box-shadow: var(--shadow-glass);
    padding: 2rem 2.5rem;
    text-align: center;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    transition: all var(--transition-normal) ease;
}

.profile-follow-section:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-card), var(--shadow-glass);
    border-color: rgba(255, 255, 255, 0.15);
}

.follow-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    font-size: 1.1rem;
}

.follow-stats span {
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.follow-stats i {
    color: var(--neon-blue);
}

.follow-stats strong {
    color: var(--text-primary);
    font-size: 1.3rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-activity-section,
    .profile-genres-section,
    .profile-recent-section,
    .profile-follow-section {
        margin: 1.5rem 1rem;
        padding: 1.5rem 1rem;
    }

    .genres-grid {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
    }

    .genre-card {
        padding: 1rem 0.75rem;
    }

    .activity-item {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .activity-icon {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .recent-track-item {
        padding: 0.5rem;
        gap: 0.75rem;
    }

    .recent-track-cover {
        width: 45px;
        height: 45px;
    }

    .follow-stats {
        gap: 2rem;
        font-size: 1rem;
    }

    .profile-hero-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .profile-hero-userinfo {
        align-items: center;
    }

    .profile-hero-title-row {
        flex-direction: column;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .genres-grid {
        grid-template-columns: 1fr 1fr;
    }

    .activity-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .recent-track-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .recent-track-time {
        justify-content: center;
    }

    .follow-stats {
        flex-direction: column;
        gap: 1rem;
    }
}
