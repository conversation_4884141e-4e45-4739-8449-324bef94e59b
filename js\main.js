class Carousel {
    constructor(container) {
        if (!container) {
            throw new Error('Carousel container element is required');
        }

        this.container = container;
        this.track = container.querySelector('.carousel-track');
        this.cards = this.track?.children;
        
        if (!this.track || !this.cards?.length) {
            throw new Error('Carousel requires a track element with child items');
        }

        this.nextButton = container.querySelector('.carousel-button.next');
        this.prevButton = container.querySelector('.carousel-button.prev');

        if (!this.nextButton || !this.prevButton) {
            throw new Error('Carousel navigation buttons not found');
        }

        this.cardWidth = this.cards[0].offsetWidth + 30; // Including gap
        this.currentIndex = 0;
        this.isAnimating = false;
        this.isLoading = true;
        this.loadedImages = 0;
        this.totalImages = this.cards.length;
        
        this.init();
    }

    init() {
        this.preloadImages();
        this.nextButton.addEventListener('click', () => this.handleNavigation('next'));
        this.prevButton.addEventListener('click', () => this.handleNavigation('prev'));
        this.updateButtonsState();

        // Add touch support
        this.setupTouchEvents();
        
        // Add keyboard navigation
        this.setupKeyboardEvents();
        
        // Update on window resize
        this.setupResizeHandler();

        // Add intersection observer
        this.setupLazyLoading();
        
        // Smooth scrolling is handled by CSS transitions on .carousel-track
        // this.setupSmoothScrolling(); 
    }

    preloadImages() {
        Array.from(this.cards).forEach(card => {
            const img = card.querySelector('img');
            if (img) {
                if (img.complete) {
                    this.handleImageLoad();
                } else {
                    img.addEventListener('load', () => this.handleImageLoad());
                }
            }
        });
    }

    handleImageLoad() {
        this.loadedImages++;
        if (this.loadedImages === this.totalImages) {
            this.isLoading = false;
            this.container.style.opacity = '1';
        }
    }

    setupLazyLoading() {
        const observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const card = entry.target;
                        const img = card.querySelector('img[data-src]');

                        // Load image if it has data-src attribute
                        if (img) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            img.addEventListener('load', () => {
                                card.classList.add('image-loaded');
                            });
                        }

                        card.classList.remove('card-loading');
                        observer.unobserve(card);
                    }
                });
            },
            {
                threshold: 0.1,
                rootMargin: '50px' // Start loading 50px before the element comes into view
            }
        );

        this.cards.forEach(card => {
            card.classList.add('card-loading');
            observer.observe(card);
        });
    }

    // setupSmoothScrolling() {
    //     let animationFrame;
    //     let targetPosition;
    //     let currentPosition = 0;
    //
    //     const animate = () => {
    //         if (!targetPosition) return;
    //
    //         const diff = targetPosition - currentPosition;
    //         const delta = diff * 0.1;
    //
    //         if (Math.abs(delta) < 0.5) {
    //             currentPosition = targetPosition;
    //             targetPosition = null;
    //         } else {
    //             currentPosition += delta;
    //         }
    //
    //         this.track.style.transform = `translateX(${-currentPosition}px)`;
    //         
    //         if (targetPosition !== null) {
    //             animationFrame = requestAnimationFrame(animate);
    //         }
    //     };
    //
    //     this.smoothScrollTo = (position) => {
    //         targetPosition = position;
    //         cancelAnimationFrame(animationFrame);
    //         animationFrame = requestAnimationFrame(animate);
    //     };
    // }

    handleNavigation(direction) {
        if (this.isAnimating) return;
        
        this.isAnimating = true;
        if (direction === 'next') {
            this.next();
        } else {
            this.prev();
        }
        setTimeout(() => this.isAnimating = false, 500);
    }

    next() {
        if (this.currentIndex < this.cards.length - this.getVisibleCards()) {
            this.currentIndex++;
            this.updatePosition();
        }
    }

    prev() {
        if (this.currentIndex > 0) {
            this.currentIndex--;
            this.updatePosition();
        }
    }

    getVisibleCards() {
        // Always return at least 1 to avoid disabling navigation
        return Math.max(1, Math.floor(this.container.offsetWidth / this.cardWidth));
    }

    // calculateInitialPosition() {
    //     const containerWidth = this.container.offsetWidth;
    //     const totalCards = this.cards.length;
    //     const cardWidth = this.cardWidth;
    //     const visibleCards = Math.floor(containerWidth / cardWidth);
    //     
    //     // Center the carousel by adjusting the starting position
    //     const offset = (containerWidth - (visibleCards * cardWidth)) / 2;
    //     this.track.style.paddingLeft = `${offset}px`;
    // }

    updatePosition() {
        const position = this.currentIndex * this.cardWidth;
        this.track.style.transform = `translateX(${-position}px)`;
        this.updateButtonsState();
    }

    updateButtonsState() {
        this.prevButton.style.opacity = this.currentIndex === 0 ? '0.5' : '1';
        this.prevButton.style.pointerEvents = this.currentIndex === 0 ? 'none' : 'auto';
        
        const maxIndex = this.cards.length - this.getVisibleCards();
        this.nextButton.style.opacity = this.currentIndex >= maxIndex ? '0.5' : '1';
        this.nextButton.style.pointerEvents = this.currentIndex >= maxIndex ? 'none' : 'auto';
    }

    setupTouchEvents() {
        let startX, moveX;
        const threshold = 50;

        this.track.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
        }, { passive: true });

        this.track.addEventListener('touchmove', (e) => {
            moveX = e.touches[0].clientX;
        }, { passive: true });

        this.track.addEventListener('touchend', () => {
            if (!startX || !moveX) return;
            
            const diff = startX - moveX;
            if (Math.abs(diff) > threshold) {
                if (diff > 0) this.handleNavigation('next');
                else this.handleNavigation('prev');
            }
            
            startX = null;
            moveX = null;
        });
    }

    setupKeyboardEvents() {
        this.container.setAttribute('tabindex', '0');
        this.container.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                this.handleNavigation('prev');
            }
            if (e.key === 'ArrowRight') {
                e.preventDefault();
                this.handleNavigation('next');
            }
            if (e.key === 'Home') {
                e.preventDefault();
                this.goToFirst();
            }
            if (e.key === 'End') {
                e.preventDefault();
                this.goToLast();
            }
        });

        // Add focus management for individual cards
        this.setupCardKeyboardNavigation();
    }

    setupCardKeyboardNavigation() {
        Array.from(this.cards).forEach((card, index) => {
            const playButton = card.querySelector('.play-button');
            const actionButton = card.querySelector('.button');

            if (playButton) {
                playButton.addEventListener('keydown', (e) => {
                    if (e.key === 'Tab' && !e.shiftKey) {
                        // Tab to next focusable element within card
                        if (actionButton) {
                            e.preventDefault();
                            actionButton.focus();
                        }
                    }
                });
            }

            if (actionButton) {
                actionButton.addEventListener('keydown', (e) => {
                    if (e.key === 'Tab' && e.shiftKey) {
                        // Shift+Tab to previous focusable element within card
                        if (playButton) {
                            e.preventDefault();
                            playButton.focus();
                        }
                    }
                });
            }
        });
    }

    goToFirst() {
        this.currentIndex = 0;
        this.updatePosition();
    }

    goToLast() {
        this.currentIndex = Math.max(0, this.cards.length - this.getVisibleCards());
        this.updatePosition();
    }

    setupResizeHandler() {
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.cardWidth = this.cards[0].offsetWidth + 30;
                this.updatePosition();
            }, 250);
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const profileButton = document.querySelector('.profile-button');
    const dropdownMenu = document.querySelector('.dropdown');

    if (profileButton && dropdownMenu) {
        profileButton.addEventListener('click', () => {
            const isExpanded = profileButton.getAttribute('aria-expanded') === 'true';
            profileButton.setAttribute('aria-expanded', !isExpanded);
            dropdownMenu.classList.toggle('show');
        });

        document.addEventListener('click', (e) => {
            if (!profileButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
                dropdownMenu.classList.remove('show');
                profileButton.setAttribute('aria-expanded', 'false');
            }
        });

        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && dropdownMenu.classList.contains('show')) {
                dropdownMenu.classList.remove('show');
                profileButton.setAttribute('aria-expanded', 'false');
            }
        });
    }

    // Initialize all carousels with a slight delay to ensure proper rendering
    const carouselContainers = document.querySelectorAll('.carousel-container');
    if (carouselContainers.length > 0) {
        carouselContainers.forEach(container => {
            setTimeout(() => {
                new Carousel(container); // Initialize a new Carousel for each container
            }, 100);
        });
    }

    // Initialize mobile navigation
    initializeMobileNavigation();
});

// Mobile Navigation Manager
function initializeMobileNavigation() {
    const hamburger = document.getElementById('hamburger');
    const menu = document.getElementById('menu');
    const overlay = document.getElementById('mobileOverlay');

    if (!hamburger || !menu || !overlay) return;

    let isOpen = false;

    function toggleMenu() {
        if (isOpen) {
            closeMenu();
        } else {
            openMenu();
        }
    }

    function openMenu() {
        isOpen = true;
        hamburger.classList.add('active');
        menu.classList.add('active');
        overlay.classList.add('active');
        hamburger.setAttribute('aria-expanded', 'true');

        // Prevent body scroll
        document.body.style.overflow = 'hidden';

        // Focus first menu item for accessibility
        const firstMenuItem = menu.querySelector('a');
        if (firstMenuItem) {
            setTimeout(() => firstMenuItem.focus(), 300);
        }
    }

    function closeMenu() {
        isOpen = false;
        hamburger.classList.remove('active');
        menu.classList.remove('active');
        overlay.classList.remove('active');
        hamburger.setAttribute('aria-expanded', 'false');

        // Restore body scroll
        document.body.style.overflow = '';

        // Return focus to hamburger button
        hamburger.focus();
    }

    // Event listeners
    hamburger.addEventListener('click', toggleMenu);
    overlay.addEventListener('click', closeMenu);

    // Close menu when clicking on menu links
    menu.querySelectorAll('a').forEach(link => {
        link.addEventListener('click', closeMenu);
    });

    // Close menu on escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && isOpen) {
            closeMenu();
        }
    });

    // Handle window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768 && isOpen) {
            closeMenu();
        }
    });
}
