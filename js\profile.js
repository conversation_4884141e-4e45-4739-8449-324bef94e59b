// Enhanced Profile Page Management System with Backend Integration
class ProfilePageManager {
    constructor() {
        this.apiBase = 'http://localhost:3001/api';
        this.currentPeriod = 'month';
        this.init();
    }

    async init() {
        this.bindElements();
        this.bindEvents();
        this.initializeAnimations();

        // Load dynamic content from backend
        await this.loadBackendData();
    }

    bindElements() {
        // Profile elements
        this.profileTitle = document.querySelector('.profile-hero-title');
        this.profileBio = document.getElementById('profileHeroBio');
        this.profileAvatar = document.getElementById('profileHeroAvatar');
        this.profileStats = document.querySelectorAll('.profile-stat-number');

        // Section containers
        this.topTracksContainer = document.querySelector('.profile-favorite-section .profile-cards');
        this.topArtistsContainer = document.querySelector('.profile-artists-section .profile-cards');
        this.genresContainer = document.querySelector('.genres-grid');
        this.listeningHistoryContainer = document.querySelector('.profile-listening-history');

        // Controls
        this.periodButtons = document.querySelectorAll('.period-btn');
        this.shareButton = document.querySelector('.profile-share-btn');
        this.settingsButton = document.querySelector('.profile-settings-btn');

        // ARIA live region
        this.liveRegion = document.getElementById('aria-live-region');
    }

    bindEvents() {
        // Period filter buttons
        this.periodButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.changePeriod(e.target.dataset.period);
            });
        });

        // Profile actions
        if (this.shareButton) {
            this.shareButton.addEventListener('click', () => {
                this.shareProfile();
            });
        }

        if (this.settingsButton) {
            this.settingsButton.addEventListener('click', () => {
                window.location.href = 'settings.html';
            });
        }

        // Bio editing
        if (this.profileBio) {
            this.profileBio.addEventListener('blur', () => {
                this.saveBio();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                this.shareProfile();
            }
        });
    }

    initializeAnimations() {
        // Animate profile stats on page load
        this.animateStats();

        // Add intersection observer for sections
        this.observeSections();
    }

    // ===== BACKEND INTEGRATION METHODS =====

    async loadBackendData() {
        try {
            console.log('🎵 Loading Profile data from backend...');

            // Load all profile data in parallel
            const [
                profileResponse,
                topTracksResponse,
                topArtistsResponse,
                genresResponse,
                listeningHistoryResponse
            ] = await Promise.all([
                fetch(`${this.apiBase}/profile`),
                fetch(`${this.apiBase}/profile/top-tracks?period=${this.currentPeriod}&limit=6`),
                fetch(`${this.apiBase}/profile/top-artists?period=${this.currentPeriod}&limit=6`),
                fetch(`${this.apiBase}/profile/genres`),
                fetch(`${this.apiBase}/profile/listening-history?limit=10`)
            ]);

            // Process responses
            if (profileResponse.ok) {
                const profile = await profileResponse.json();
                this.updateProfileInfo(profile);
            }

            if (topTracksResponse.ok) {
                const topTracks = await topTracksResponse.json();
                this.renderTopTracks(topTracks);
            }

            if (topArtistsResponse.ok) {
                const topArtists = await topArtistsResponse.json();
                this.renderTopArtists(topArtists);
            }

            if (genresResponse.ok) {
                const genres = await genresResponse.json();
                this.renderGenreBreakdown(genres);
            }

            if (listeningHistoryResponse.ok) {
                const history = await listeningHistoryResponse.json();
                this.renderListeningHistory(history);
            }

            console.log('✅ Profile data loaded successfully!');
            this.announceAction('Profile loaded with fresh data!');

        } catch (error) {
            console.error('❌ Error loading backend data:', error);
            this.announceAction('Using demo content - server unavailable');
        }
    }

    updateProfileInfo(profile) {
        // Update profile header
        if (this.profileTitle) {
            this.profileTitle.textContent = `Welcome, ${profile.displayName || profile.username}!`;
        }

        if (this.profileBio && profile.bio) {
            this.profileBio.textContent = profile.bio;
        }

        // Update stats with animation
        if (this.profileStats.length >= 4) {
            this.animateStatNumber(this.profileStats[0], profile.stats.totalPlaylists);
            this.animateStatNumber(this.profileStats[1], profile.stats.totalLikedSongs);
            this.animateStatNumber(this.profileStats[2], profile.stats.followers);
            this.animateStatNumber(this.profileStats[3], profile.stats.totalListeningTime);
        }

        // Update follower info
        const followedBy = document.querySelector('.profile-followed-by strong');
        if (followedBy) {
            followedBy.textContent = profile.stats.followers;
        }

        console.log('📊 Profile info updated:', profile);
    }

    animateStatNumber(element, targetValue) {
        const startValue = parseInt(element.textContent) || 0;
        const duration = 1500; // 1.5 seconds
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.round(startValue + (targetValue - startValue) * easeOutQuart);

            element.textContent = currentValue;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    renderTopTracks(tracks) {
        if (!this.topTracksContainer || !tracks.length) return;

        this.topTracksContainer.innerHTML = tracks.map(track => `
            <div class="profile-card track-card" data-track-id="${track.id}">
                <div class="profile-card-image">
                    <img src="${track.cover}" alt="${track.title}" loading="lazy">
                    <div class="profile-card-overlay">
                        <button type="button" class="profile-play-btn" aria-label="Play ${track.title}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="track-rank">#${track.rank}</div>
                </div>
                <div class="profile-card-content">
                    <h3 class="profile-card-title">${track.title}</h3>
                    <p class="profile-card-subtitle">${track.artist}</p>
                    <p class="profile-card-meta">${track.playCount} plays this ${track.period}</p>
                </div>
            </div>
        `).join('');

        this.bindTrackCardEvents();
        console.log(`✅ Rendered ${tracks.length} top tracks`);
    }

    renderTopArtists(artists) {
        if (!this.topArtistsContainer || !artists.length) return;

        this.topArtistsContainer.innerHTML = artists.map(artist => `
            <div class="profile-card artist-card" data-artist="${artist.name}">
                <div class="profile-card-image artist">
                    <img src="${artist.image}" alt="${artist.name}" loading="lazy">
                    <div class="profile-card-overlay">
                        <button type="button" class="profile-play-btn" aria-label="Play ${artist.name}">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="artist-rank">#${artist.rank}</div>
                </div>
                <div class="profile-card-content">
                    <h3 class="profile-card-title">${artist.name}</h3>
                    <p class="profile-card-subtitle">${artist.genre}</p>
                    <p class="profile-card-meta">${artist.totalPlays} total plays</p>
                </div>
            </div>
        `).join('');

        this.bindArtistCardEvents();
        console.log(`✅ Rendered ${artists.length} top artists`);
    }

    renderGenreBreakdown(genres) {
        if (!this.genresContainer || !genres.length) return;

        this.genresContainer.innerHTML = genres.slice(0, 6).map(genre => {
            const emoji = this.getGenreEmoji(genre.name);
            return `
                <div class="genre-card" data-genre="${genre.name.toLowerCase()}">
                    <div class="genre-icon">${emoji}</div>
                    <h3>${genre.name}</h3>
                    <p>${genre.percentage}% of listening time</p>
                    <div class="genre-progress">
                        <div class="genre-progress-fill" style="width: ${genre.percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');

        console.log(`✅ Rendered ${genres.length} genre breakdown`);
    }

    getGenreEmoji(genre) {
        const emojiMap = {
            'Pop': '🎵',
            'Electronic': '⚡',
            'Indie': '🎸',
            'Rock': '🤘',
            'Hip Hop': '🎤',
            'Jazz': '🎺',
            'Classical': '🎼',
            'Country': '🤠',
            'R&B': '💫',
            'Folk': '🌿'
        };
        return emojiMap[genre] || '🎶';
    }

    renderListeningHistory(history) {
        // This would render in a listening history section if it exists
        console.log(`📱 Listening history: ${history.length} recent tracks`);
    }

    bindTrackCardEvents() {
        this.topTracksContainer.querySelectorAll('.profile-play-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const card = btn.closest('.track-card');
                const trackId = card.dataset.trackId;
                this.playTrack(trackId);
            });
        });
    }

    bindArtistCardEvents() {
        this.topArtistsContainer.querySelectorAll('.profile-play-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const card = btn.closest('.artist-card');
                const artistName = card.dataset.artist;
                this.playArtist(artistName);
            });
        });
    }

    playTrack(trackId) {
        this.announceAction(`Playing track ${trackId}`);
        console.log(`🎵 Playing track: ${trackId}`);
    }

    playArtist(artistName) {
        this.announceAction(`Playing ${artistName}`);
        console.log(`🎵 Playing artist: ${artistName}`);
    }

    animateStats() {
        // Trigger stat animations with staggered timing
        this.profileStats.forEach((stat, index) => {
            setTimeout(() => {
                stat.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    stat.style.transform = 'scale(1)';
                }, 200);
            }, index * 100);
        });
    }

    observeSections() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach((entry, index) => {
                if (entry.isIntersecting) {
                    setTimeout(() => {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }, index * 100);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // Observe sections for animation
        const sections = document.querySelectorAll('.profile-section, .profile-favorite-section, .profile-artists-section, .profile-genres-section');
        sections.forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(30px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });
    }

    async changePeriod(period) {
        this.currentPeriod = period;

        // Update active button
        this.periodButtons.forEach(btn => btn.classList.remove('active'));
        document.querySelector(`[data-period="${period}"]`)?.classList.add('active');

        // Reload time-based data
        try {
            const [topTracksResponse, topArtistsResponse] = await Promise.all([
                fetch(`${this.apiBase}/profile/top-tracks?period=${period}&limit=6`),
                fetch(`${this.apiBase}/profile/top-artists?period=${period}&limit=6`)
            ]);

            if (topTracksResponse.ok) {
                const topTracks = await topTracksResponse.json();
                this.renderTopTracks(topTracks);
            }

            if (topArtistsResponse.ok) {
                const topArtists = await topArtistsResponse.json();
                this.renderTopArtists(topArtists);
            }

            this.announceAction(`Updated to ${period} view`);
        } catch (error) {
            console.error('Error updating period:', error);
        }
    }

    shareProfile() {
        if (navigator.share) {
            navigator.share({
                title: 'My Banshee Music Profile',
                text: 'Check out my music taste on Banshee!',
                url: window.location.href
            });
        } else {
            // Fallback: copy to clipboard
            navigator.clipboard.writeText(window.location.href);
            this.announceAction('Profile link copied to clipboard!');
        }
    }

    saveBio() {
        const bioText = this.profileBio.textContent.trim();
        if (bioText && bioText !== 'Click here to add a short bio about yourself...') {
            // Here you would save to backend
            console.log('💾 Saving bio:', bioText);
            this.announceAction('Bio saved!');
        }
    }

    announceAction(message) {
        if (this.liveRegion) {
            this.liveRegion.textContent = message;

            // Clear the message after a short delay
            setTimeout(() => {
                this.liveRegion.textContent = '';
            }, 1000);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.profileManager = new ProfilePageManager();
});

// Export for potential module use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProfilePageManager;
}