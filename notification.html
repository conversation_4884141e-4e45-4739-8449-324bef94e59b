<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notifications - BansheeBlast</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/notification.css">
</head>


<body>
    <a href="#main-content" class="skip-link">Skip to main content</a>
    <header>
        <nav aria-label="Primary Navigation">
            <a class="logo" href="subscription.html" aria-label="Go to subscription page">
                <img src="imgs/logo-B.png" alt="Banshee Music Logo" loading="lazy">
            </a>
            <ul class="menu">
                <li><a href="index.html" aria-current="page">Home</a></li>
                <li><a href="explore.html">Explore</a></li>
                <li><a href="library.html">Library</a></li>
                <li><a href="player.html">Player</a></li>
            </ul>
            <div class="user-profile">
                <button type="button" class="profile-button" aria-expanded="false" aria-controls="dropdown-menu" aria-label="User Profile Menu">
                    <img class="profile-icon" src="imgs/profile-icon-B.png" alt="User Profile Icon" loading="lazy">
                </button>
                <div class="dropdown" id="dropdown-menu">
                    <ul>
                        <li><a href="profile.html">Profile</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li><a href="notification.html" aria-current="page">Notifications</a></li>
                        <li><button type="button" class="logout-button">Logout</button></li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>


    
    <main id="main-content" class="container">
        <div class="notifications-page">
            <div class="page-header">
                <h1 class="notification-title-gradient">Notifications</h1>
                <p class="page-description">Stay updated with your music activity and app updates</p>
            </div>

            <div class="notifications-controls">
                <div class="controls-left">
                    <div class="filter-tabs">
                        <button type="button" class="filter-tab active" data-filter="all">All</button>
                        <button type="button" class="filter-tab" data-filter="music">Music</button>
                        <button type="button" class="filter-tab" data-filter="social">Social</button>
                        <button type="button" class="filter-tab" data-filter="system">System</button>
                    </div>
                </div>
                <div class="controls-right">
                    <button type="button" id="markAllReadBtn" class="action-btn mark-all-btn">
                        <i class="fas fa-check-double"></i>
                        Mark All as Read
                    </button>
                    <button type="button" id="clearAllBtn" class="action-btn clear-all-btn">
                        <i class="fas fa-trash"></i>
                        Clear All
                    </button>
                </div>
            </div>

            <div class="notifications-container">
                <div class="notifications-list" id="notificationsList">
                    <!-- Notifications will be dynamically inserted here -->
                </div>

                <div class="empty-state hidden" id="emptyState">
                    <div class="empty-icon">
                        <i class="fas fa-bell-slash"></i>
                    </div>
                    <h3>No notifications yet</h3>
                    <p>When you have new activity, it will appear here</p>
                </div>
            </div>
        </div>

        <div id="aria-live-region" aria-live="polite" class="sr-only"></div>
    </main>
    <script src="js/notification.js"></script>
</body>
</html>